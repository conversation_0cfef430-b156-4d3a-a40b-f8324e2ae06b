
import { Button } from "@/components/ui/button";
import { Clock, Users, Video, ArrowRight } from "lucide-react";

export const ServicesSection = () => {
  const services = [
    {
      icon: Clock,
      title: "Free 15-Minute Strategy Session",
      subtitle: "Get Unstuck, Get Strategic",
      description: "A powerful introduction to executive-level thinking and strategic planning.",
      gradient: "from-emerald-500 to-teal-600",
      bgGradient: "from-emerald-500/10 to-teal-600/10"
    },
    {
      icon: Users,
      title: "Executive Career Coaching",
      subtitle: "Comprehensive Packages",
      description: "Structured coaching programs designed to accelerate your path to C-suite leadership.",
      gradient: "from-purple-500 to-indigo-600",
      bgGradient: "from-purple-500/10 to-indigo-600/10"
    },
    {
      icon: Video,
      title: "From Hood to Hooded Series",
      subtitle: "Animated Inspiration",
      description: "Insights, shared experiences, and boardroom-level inspiration through engaging storytelling.",
      gradient: "from-amber-500 to-orange-600",
      bgGradient: "from-amber-500/10 to-orange-600/10"
    }
  ];

  return (
    <section className="py-20 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent mb-6">
            What You Offer
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Transform your career trajectory with strategic coaching and inspiring content
          </p>
        </div>
        
        {/* Bento Grid Layout */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {services.map((service, index) => (
            <div key={index} className={`backdrop-blur-xl bg-gradient-to-br ${service.bgGradient} rounded-3xl border border-white/10 p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 group ${index === 0 ? 'md:col-span-2 lg:col-span-1' : ''}`}>
              <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-r ${service.gradient} shadow-lg mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <service.icon className="h-8 w-8 text-white" />
              </div>
              
              <h3 className="text-2xl font-bold text-white mb-2">{service.title}</h3>
              <p className="text-lg font-medium text-purple-200 mb-4">{service.subtitle}</p>
              <p className="text-gray-300 leading-relaxed mb-6">{service.description}</p>
              
              <Button variant="ghost" className="text-white hover:bg-white/10 group/btn">
                Learn More 
                <ArrowRight className="ml-2 h-4 w-4 group-hover/btn:translate-x-1 transition-transform" />
              </Button>
            </div>
          ))}
        </div>
        
        {/* YouTube Preview Section */}
        <div className="mt-16 backdrop-blur-xl bg-white/5 rounded-3xl border border-white/10 p-8 shadow-xl">
          <h3 className="text-2xl font-bold text-white mb-6 text-center">Recent YouTube Episodes</h3>
          <div className="grid md:grid-cols-3 gap-6">
            {[1, 2, 3].map((_, index) => (
              <div key={index} className="backdrop-blur-sm bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl border border-white/20 aspect-video flex items-center justify-center hover:scale-105 transition-transform duration-300 cursor-pointer group">
                <div className="text-center text-white">
                  <Video className="h-12 w-12 mx-auto mb-2 group-hover:scale-110 transition-transform" />
                  <p className="text-sm">Episode Thumbnail</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

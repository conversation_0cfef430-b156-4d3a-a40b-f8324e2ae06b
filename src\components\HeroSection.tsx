
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>, Play } from "lucide-react";

export const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex flex-col overflow-hidden">
      {/* Header image with gradient border */}
      <div className="relative w-full h-32 md:h-48 lg:h-64 z-20">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500 via-pink-500 to-amber-500 p-1">
          <div className="w-full h-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
            <img
              src="/images/header.png"
              alt="From Hood to Hooded Header"
              className="w-full h-full object-cover opacity-90"
            />
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-slate-900/50" />
          </div>
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 flex items-center justify-center relative px-4">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-amber-500/20 to-orange-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-purple-500/5 to-blue-500/5 rounded-full blur-3xl animate-spin-slow" />
        </div>

        {/* Glass morphism container */}
        <div className="relative z-10 text-center max-w-6xl mx-auto">
          <div className="backdrop-blur-xl bg-white/10 rounded-3xl border border-white/20 p-8 md:p-16 shadow-2xl">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-amber-500/20 to-orange-500/20 backdrop-blur-sm rounded-full px-6 py-2 mb-8 border border-amber-500/30">
              <Star className="h-4 w-4 text-amber-300" />
              <span className="text-amber-200 text-sm font-medium">Executive Coaching for Black Women</span>
            </div>

            {/* Main headline */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-white via-purple-200 to-amber-200 bg-clip-text text-transparent mb-6 leading-tight">
              From Hood to Hooded
            </h1>

            <p className="text-xl md:text-2xl text-purple-200 mb-4 font-light">
              Built for the Boardroom. Bred on the Block.
            </p>

            <p className="text-lg md:text-xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              C‑suite coaching and storytelling for Black women overcoming bias and building legacy.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button
                size="lg"
                className="bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white font-semibold px-8 py-6 rounded-full text-lg shadow-2xl hover:shadow-amber-500/25 transition-all duration-300 hover:scale-105 group"
              >
                Book Your Free 15‑Minute Strategy Call
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>

              <Button variant="outline" size="lg" className="border-2 border-white/30 text-white hover:bg-white/10 px-8 py-4 text-lg font-semibold rounded-full backdrop-blur-sm">
                <Play className="mr-2 h-5 w-5" />
                Watch My Story
              </Button>
            </div>

            <p className="text-sm text-gray-400 mb-8">
              Take a powerful step toward the executive table.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
              <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-white/10">
                <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2">500+</div>
                <div className="text-gray-300 font-medium">Lives Transformed</div>
              </div>
              <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-pink-500/10 to-amber-500/10 backdrop-blur-sm border border-white/10">
                <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-pink-400 to-amber-400 bg-clip-text text-transparent mb-2">98%</div>
                <div className="text-gray-300 font-medium">Success Rate</div>
              </div>
              <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-amber-500/10 to-orange-500/10 backdrop-blur-sm border border-white/10">
                <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-2">15+</div>
                <div className="text-gray-300 font-medium">Years Experience</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-20">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gradient-to-b from-purple-400 to-amber-400 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  );
};

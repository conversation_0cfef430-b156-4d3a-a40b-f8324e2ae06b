
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";

export const HeroSection = () => {
  return (
    <section className="relative min-h-screen w-full overflow-hidden">
      {/* Video Background */}
      <div className="absolute inset-0 w-full h-full">
        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-full h-full object-cover"
        >
          <source src="/video/vid.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
      </div>

      {/* Dark Overlay for text contrast */}
      <div className="absolute inset-0 bg-black/70 z-10"></div>

      {/* Main content area - centered and full screen */}
      <div className="relative z-20 flex items-center justify-center min-h-screen px-4 py-16">
        {/* Subtle animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-white/5 to-gray-200/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-gray-200/5 to-white/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-white/3 to-gray-100/3 rounded-full blur-3xl animate-spin-slow" />
        </div>

        {/* Content container */}
        <div className="relative z-30 text-center max-w-6xl mx-auto">
          <div className="p-8 md:p-16">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-slate-500/30 to-gray-600/30 backdrop-blur-sm rounded-full px-6 py-2 mb-8 border border-white/30">
              <Star className="h-4 w-4 text-white" />
              <span className="text-white text-sm font-medium">Executive Coaching for Black Women</span>
            </div>

            {/* Main headline */}
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight drop-shadow-2xl">
              From Hood to Hooded
            </h1>

            <p className="text-xl md:text-2xl text-gray-200 mb-4 font-light drop-shadow-lg">
              Built for the Boardroom. Bred on the Block.
            </p>

            <p className="text-lg md:text-xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed drop-shadow-lg">
              C‑suite coaching and storytelling for Black women overcoming bias and building legacy.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button
                size="lg"
                className="bg-gradient-to-r from-slate-600 to-gray-700 hover:from-slate-700 hover:to-gray-800 text-white font-semibold px-8 py-6 rounded-full text-lg shadow-2xl hover:shadow-slate-500/25 transition-all duration-300 hover:scale-105 group border-2 border-transparent hover:border-white/20"
              >
                Book Your Free 15‑Minute Strategy Call
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>

              <Button variant="outline" size="lg" className="border-2 border-white/50 text-white hover:bg-white/20 px-8 py-4 text-lg font-semibold rounded-full backdrop-blur-sm">
                <Play className="mr-2 h-5 w-5" />
                Watch My Story
              </Button>
            </div>

            <p className="text-sm text-gray-300 mb-8 drop-shadow-lg">
              Take a powerful step toward the executive table.
            </p>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
              <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-black/30 to-gray-900/30 backdrop-blur-sm border border-white/20">
                <div className="text-3xl lg:text-4xl font-bold text-white mb-2 drop-shadow-lg">500+</div>
                <div className="text-gray-200 font-medium">Lives Transformed</div>
              </div>
              <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-gray-900/30 to-black/30 backdrop-blur-sm border border-white/20">
                <div className="text-3xl lg:text-4xl font-bold text-white mb-2 drop-shadow-lg">98%</div>
                <div className="text-gray-200 font-medium">Success Rate</div>
              </div>
              <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-black/30 to-gray-800/30 backdrop-blur-sm border border-white/20">
                <div className="text-3xl lg:text-4xl font-bold text-white mb-2 drop-shadow-lg">15+</div>
                <div className="text-gray-200 font-medium">Years Experience</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-30">
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gradient-to-b from-white to-gray-300 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  );
};

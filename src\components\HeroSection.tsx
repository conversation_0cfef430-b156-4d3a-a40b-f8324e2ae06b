
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";

export const HeroSection = () => {
  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden px-4">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-amber-500/20 to-orange-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
      
      {/* Glass morphism container */}
      <div className="relative z-10 text-center max-w-6xl mx-auto">
        <div className="backdrop-blur-xl bg-white/10 rounded-3xl border border-white/20 p-8 md:p-16 shadow-2xl">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-amber-500/20 to-orange-500/20 backdrop-blur-sm rounded-full px-6 py-2 mb-8 border border-amber-500/30">
            <Star className="h-4 w-4 text-amber-300" />
            <span className="text-amber-200 text-sm font-medium">Executive Coaching for Black Women</span>
          </div>
          
          {/* Main headline */}
          <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-white via-purple-200 to-amber-200 bg-clip-text text-transparent mb-6 leading-tight">
            From Hood to Hooded
          </h1>
          
          <p className="text-xl md:text-2xl text-purple-200 mb-4 font-light">
            Built for the Boardroom. Bred on the Block.
          </p>
          
          <p className="text-lg md:text-xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
            C‑suite coaching and storytelling for Black women overcoming bias and building legacy.
          </p>
          
          {/* CTA Button */}
          <Button 
            size="lg" 
            className="bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white font-semibold px-8 py-6 rounded-full text-lg shadow-2xl hover:shadow-amber-500/25 transition-all duration-300 hover:scale-105 group"
          >
            Book Your Free 15‑Minute Strategy Call
            <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
          </Button>
          
          <p className="text-sm text-gray-400 mt-4">
            Take a powerful step toward the executive table.
          </p>
        </div>
      </div>
    </section>
  );
};

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Star } from "lucide-react";

export const BannerSection = () => {
  return (
    <section className="relative py-20 px-4 overflow-hidden">
      {/* Background with banner image */}
      <div className="absolute inset-0">
        <div className="relative w-full h-full">
          {/* Gradient border effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500 via-pink-500 to-amber-500 p-1">
            <div className="w-full h-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
              <img 
                src="/images/banner.png" 
                alt="From Hood to Hooded Banner" 
                className="w-full h-full object-cover opacity-60"
              />
              {/* Overlay gradients */}
              <div className="absolute inset-0 bg-gradient-to-r from-slate-900/80 via-purple-900/60 to-slate-900/80" />
              <div className="absolute inset-0 bg-gradient-to-t from-slate-900/90 via-transparent to-slate-900/50" />
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto max-w-6xl">
        <div className="text-center">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-amber-500/20 to-orange-500/20 backdrop-blur-sm rounded-full px-6 py-2 mb-8 border border-amber-500/30">
            <Star className="h-4 w-4 text-amber-300" />
            <span className="text-amber-200 text-sm font-medium">Limited Time Opportunity</span>
          </div>

          {/* Main Message */}
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-white via-purple-200 to-amber-200 bg-clip-text text-transparent mb-6 leading-tight">
            Your Transformation
            <br />
            <span className="bg-gradient-to-r from-amber-400 via-pink-400 to-purple-400 bg-clip-text text-transparent">
              Starts Today
            </span>
          </h2>

          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
            Don't let another day pass wondering "what if." The path from hood to hooded 
            is waiting for you. Take the first step toward the executive table you deserve.
          </p>

          {/* CTA Section */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-amber-500 via-orange-500 to-red-500 hover:from-amber-600 hover:via-orange-600 hover:to-red-600 text-white font-bold px-10 py-6 text-xl rounded-full shadow-2xl transform hover:scale-105 transition-all duration-300 group border-2 border-transparent hover:border-white/20"
            >
              Claim Your Free Strategy Session
              <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>

          {/* Urgency Message */}
          <div className="bg-gradient-to-r from-red-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl p-6 border border-red-500/30 max-w-2xl mx-auto">
            <p className="text-red-200 font-semibold text-lg mb-2">
              🔥 Only 10 Strategy Sessions Available This Month
            </p>
            <p className="text-gray-300">
              These complimentary sessions are designed to create a personalized roadmap 
              for your leadership transformation. Book yours before they're gone.
            </p>
          </div>

          {/* Social Proof */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20">
              <div className="text-3xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-2">
                24 Hours
              </div>
              <div className="text-gray-300 font-medium">Average Response Time</div>
            </div>
            
            <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20">
              <div className="text-3xl font-bold bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent mb-2">
                100%
              </div>
              <div className="text-gray-300 font-medium">Confidential & Safe</div>
            </div>
            
            <div className="text-center p-6 rounded-2xl bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20">
              <div className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent mb-2">
                No Pressure
              </div>
              <div className="text-gray-300 font-medium">Just Pure Value</div>
            </div>
          </div>
        </div>
      </div>

      {/* Decorative elements */}
      <div className="absolute top-10 left-10 w-32 h-32 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-2xl animate-pulse" />
      <div className="absolute bottom-10 right-10 w-40 h-40 bg-gradient-to-r from-amber-500/20 to-orange-500/20 rounded-full blur-2xl animate-pulse delay-1000" />
    </section>
  );
};

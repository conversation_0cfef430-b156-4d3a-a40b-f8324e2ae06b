import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Award, Users, BookOpen, Target } from "lucide-react";

export const CoachSection = () => {
  const achievements = [
    {
      icon: Award,
      title: "Executive Coach",
      description: "Certified leadership development specialist"
    },
    {
      icon: Users,
      title: "500+ Leaders",
      description: "Transformed careers and lives"
    },
    {
      icon: BookOpen,
      title: "Author & Speaker",
      description: "Published thought leader"
    },
    {
      icon: Target,
      title: "Results-Driven",
      description: "98% client success rate"
    }
  ];

  const credentials = [
    "PhD in Leadership Development",
    "Certified Executive Coach (ICF)",
    "15+ Years Corporate Experience",
    "Fortune 500 Consultant",
    "TEDx Speaker",
    "Published Author"
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Coach Image */}
          <div className="relative">
            <div className="relative w-full max-w-lg mx-auto">
              {/* Gradient border */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 via-pink-500 to-amber-500 p-1 rounded-3xl">
                <div className="w-full h-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 rounded-3xl overflow-hidden">
                  <img 
                    src="/images/coach.jpg" 
                    alt="Dr. Jennifer R. Bishop - Executive Coach" 
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              
              {/* Floating achievement cards */}
              <div className="absolute -top-6 -right-6 bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                <div className="text-center">
                  <div className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">15+</div>
                  <div className="text-white text-sm">Years</div>
                </div>
              </div>
              
              <div className="absolute -bottom-6 -left-6 bg-gradient-to-r from-amber-500/20 to-orange-500/20 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
                <div className="text-center">
                  <div className="text-2xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent">500+</div>
                  <div className="text-white text-sm">Leaders</div>
                </div>
              </div>
            </div>
          </div>

          {/* Coach Content */}
          <div className="space-y-8">
            <div>
              <Badge className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-200 border-purple-500/30 mb-4">
                Meet Your Coach
              </Badge>
              
              <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-6">
                Dr. Jennifer R. Bishop
              </h2>
              
              <p className="text-xl text-purple-200 mb-6 font-light">
                Executive Coach & Leadership Transformation Specialist
              </p>
              
              <div className="space-y-4 text-gray-300 leading-relaxed">
                <p>
                  From growing up in challenging circumstances to earning a PhD and coaching Fortune 500 executives, 
                  Dr. Bishop embodies the "From Hood to Hooded" transformation she helps others achieve.
                </p>
                
                <p>
                  With over 15 years of experience in corporate leadership and executive coaching, she specializes 
                  in empowering Black women to break through barriers, overcome bias, and build lasting legacies 
                  in the C-suite.
                </p>
                
                <p>
                  Her unique approach combines academic rigor with street-smart wisdom, creating a coaching 
                  experience that's both transformational and deeply authentic.
                </p>
              </div>
            </div>

            {/* Credentials */}
            <div>
              <h3 className="text-xl font-semibold text-white mb-4">Credentials & Expertise</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {credentials.map((credential, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"></div>
                    <span className="text-gray-300">{credential}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Achievement Cards */}
            <div className="grid grid-cols-2 gap-4">
              {achievements.map((achievement, index) => (
                <Card key={index} className="bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm border border-white/20">
                  <CardContent className="p-4 text-center">
                    <achievement.icon className="w-8 h-8 mx-auto mb-2 text-purple-400" />
                    <h4 className="font-semibold text-white text-sm mb-1">{achievement.title}</h4>
                    <p className="text-gray-400 text-xs">{achievement.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* CTA */}
            <div className="pt-6">
              <Button 
                size="lg" 
                className="bg-gradient-to-r from-purple-600 via-pink-600 to-amber-600 hover:from-purple-700 hover:via-pink-700 hover:to-amber-700 text-white font-semibold px-8 py-4 rounded-full shadow-2xl transform hover:scale-105 transition-all duration-300 group"
              >
                Work With Dr. Bishop
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

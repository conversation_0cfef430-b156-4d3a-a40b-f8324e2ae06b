import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Award, Users, BookOpen, Target } from "lucide-react";

export const CoachSection = () => {
  const credentials = [
    "PhD in Leadership Development",
    "Certified Executive Coach (ICF)",
    "15+ Years Corporate Experience",
    "Fortune 500 Consultant",
    "TEDx Speaker",
    "Published Author"
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-slate-50 via-gray-100 to-slate-200">
      <div className="container mx-auto max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Coach Image */}
          <div className="relative">
            <div className="relative w-full max-w-lg mx-auto">
              {/* Subtle gradient border */}
              <div className="absolute inset-0 bg-gradient-to-r from-slate-300 via-gray-400 to-slate-500 p-1 rounded-3xl">
                <div className="w-full h-full bg-white rounded-3xl overflow-hidden">
                  <img
                    src="/images/coach.jpg"
                    alt="Dr. Jennifer R. Bishop - Executive Coach"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Coach Content */}
          <div className="space-y-8">
            <div>
              <Badge className="bg-gradient-to-r from-slate-500/20 to-gray-600/20 text-slate-700 border-slate-400/30 mb-4">
                Meet Your Coach
              </Badge>

              <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-800 via-gray-700 to-slate-600 bg-clip-text text-transparent mb-6">
                Dr. Jennifer R. Bishop
              </h2>

              <p className="text-xl text-slate-700 mb-6 font-light">
                Executive Coach & Leadership Transformation Specialist
              </p>

              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  From growing up in challenging circumstances to earning a PhD and coaching Fortune 500 executives,
                  Dr. Bishop embodies the "From Hood to Hooded" transformation she helps others achieve.
                </p>

                <p>
                  With over 15 years of experience in corporate leadership and executive coaching, she specializes
                  in empowering Black women to break through barriers, overcome bias, and build lasting legacies
                  in the C-suite.
                </p>

                <p>
                  Her unique approach combines academic rigor with street-smart wisdom, creating a coaching
                  experience that's both transformational and deeply authentic.
                </p>
              </div>
            </div>

            {/* Credentials */}
            <div>
              <h3 className="text-xl font-semibold text-slate-800 mb-4">Credentials & Expertise</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {credentials.map((credential, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-slate-600 to-gray-700 rounded-full"></div>
                    <span className="text-gray-600">{credential}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Enhanced CTA Button */}
            <div className="pt-8">
              <Button
                size="lg"
                className="bg-gradient-to-r from-slate-700 to-gray-800 hover:from-slate-800 hover:to-gray-900 text-white font-bold px-12 py-6 text-xl rounded-full shadow-2xl transform hover:scale-105 transition-all duration-300 group border-2 border-transparent hover:border-slate-500/30"
              >
                Work With Dr. Bishop
                <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

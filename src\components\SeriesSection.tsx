
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Play, ArrowRight, Film, Sparkles } from "lucide-react";

export const SeriesSection = () => {
  return (
    <section className="py-32 px-4 relative overflow-hidden">
      {/* Enhanced background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-600/20 to-indigo-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-gradient-to-r from-pink-600/20 to-rose-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
      
      <div className="max-w-7xl mx-auto relative z-10">
        <div className="backdrop-blur-3xl bg-gradient-to-br from-purple-500/15 to-pink-500/15 rounded-[2rem] border border-purple-500/30 p-12 md:p-16 shadow-2xl relative overflow-hidden">
          {/* Floating elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-8 left-8">
              <Film className="h-8 w-8 text-purple-400 animate-bounce" />
            </div>
            <div className="absolute top-12 right-12">
              <Sparkles className="h-6 w-6 text-pink-400 animate-pulse" />
            </div>
            <div className="absolute bottom-8 left-12">
              <div className="w-4 h-4 bg-purple-400 rounded-full animate-pulse"></div>
            </div>
          </div>
          
          <div className="grid lg:grid-cols-2 gap-16 items-center relative z-10">
            {/* Enhanced Content */}
            <div>
              <div className="inline-flex items-center gap-3 bg-gradient-to-r from-purple-500/30 to-pink-500/30 backdrop-blur-sm rounded-full px-6 py-3 mb-8 border border-purple-500/40">
                <Film className="h-5 w-5 text-purple-300" />
                <span className="text-purple-200 font-semibold">Animated Series</span>
              </div>
              
              <h2 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-8 leading-tight">
                "From Hood to Hooded"
                <span className="block text-3xl md:text-4xl mt-4 font-light">Series</span>
              </h2>
              
              <p className="text-xl text-gray-200 leading-relaxed mb-10 font-light">
                An animated reel series starring Eva, our executive avatar navigating microaggressions, code-switching, and the journey from the hood to the boardroom with wit, wisdom, and authenticity.
              </p>
              
              <Button 
                size="lg" 
                variant="outline" 
                className="border-2 border-purple-400 text-purple-200 hover:bg-purple-500/20 hover:border-purple-300 rounded-full px-10 py-6 group text-lg font-semibold backdrop-blur-sm transition-all duration-300 hover:scale-105"
              >
                <Play className="mr-3 h-6 w-6 group-hover:scale-110 transition-transform" />
                Watch Latest Episode
                <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
            
            {/* Enhanced Video Previews */}
            <div className="space-y-8">
              {[
                { title: "Eva's Boardroom Journey", subtitle: "Navigating corporate dynamics with authenticity", views: "12K views" },
                { title: "Code-Switching Chronicles", subtitle: "Finding your voice in every room", views: "8.5K views" }
              ].map((episode, index) => (
                <div key={index} className="backdrop-blur-sm bg-gradient-to-br from-amber-500/20 to-orange-500/20 rounded-2xl border border-amber-500/40 p-8 hover:scale-105 transition-all duration-500 cursor-pointer group relative overflow-hidden">
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-amber-600/10 to-orange-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
                  
                  <div className="flex items-center gap-6 relative z-10">
                    <div className="flex-shrink-0">
                      <div className="w-32 h-20 bg-gradient-to-br from-purple-500 via-pink-500 to-purple-600 rounded-xl flex items-center justify-center shadow-xl group-hover:scale-110 transition-transform duration-300 relative overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                        <Play className="h-8 w-8 text-white relative z-10" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-white font-bold text-xl mb-2">Episode {index + 1}: {episode.title}</h3>
                      <p className="text-gray-200 mb-2">{episode.subtitle}</p>
                      <p className="text-amber-300 text-sm font-medium">{episode.views}</p>
                    </div>
                  </div>
                </div>
              ))}
              
              {/* Subscribe prompt */}
              <div className="backdrop-blur-sm bg-gradient-to-br from-emerald-500/20 to-teal-500/20 rounded-2xl border border-emerald-500/30 p-6 text-center">
                <p className="text-emerald-200 font-medium mb-3">New episodes every week</p>
                <Button size="sm" variant="outline" className="border-emerald-400 text-emerald-300 hover:bg-emerald-500/20 rounded-full">
                  Subscribe for Updates
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

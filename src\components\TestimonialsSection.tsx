
import { Star, Quote, Trophy, Award } from "lucide-react";

export const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "VP of Operations",
      company: "Tech Innovators Inc.",
      content: "The coaching transformed my approach to leadership. I went from feeling overlooked to confidently commanding the boardroom.",
      rating: 5,
      avatar: "👩🏾‍💼"
    },
    {
      name: "<PERSON>",
      role: "Senior Director",
      company: "Global Finance Corp",
      content: "The 'From Hood to Hooded' perspective resonated deeply. It helped me embrace my journey and leverage my unique strengths.",
      rating: 5,
      avatar: "👩🏿‍💼"
    },
    {
      name: "<PERSON>",
      role: "C-Suite Executive",
      company: "Fortune 500 Company",
      content: "Strategic, insightful, and culturally aware coaching that actually gets results. My executive presence has never been stronger.",
      rating: 5,
      avatar: "👩🏽‍💼"
    }
  ];

  return (
    <section className="py-32 px-4 relative overflow-hidden">
      {/* Enhanced background */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-900/20 via-transparent to-pink-900/20"></div>
        <div className="absolute top-1/3 left-1/5 w-72 h-72 bg-gradient-to-r from-purple-600/20 to-indigo-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/5 w-80 h-80 bg-gradient-to-r from-pink-600/20 to-rose-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
      
      <div className="max-w-8xl mx-auto relative z-10">
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-3 bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-full px-8 py-3 mb-8 border border-purple-500/30">
            <Trophy className="h-5 w-5 text-purple-300" />
            <span className="text-purple-200 font-semibold">Success Stories</span>
          </div>
          
          <h2 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-8">
            Real Transformation
            <span className="block text-4xl md:text-6xl mt-2">From Real Women</span>
          </h2>
          
          <p className="text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Breaking barriers and building legacies across corporate America
          </p>
        </div>
        
        {/* Enhanced Bento Grid for Testimonials */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {testimonials.map((testimonial, index) => (
            <div key={index} className={`backdrop-blur-3xl bg-gradient-to-br from-white/10 to-purple-500/10 rounded-3xl border border-white/20 p-10 shadow-2xl hover:shadow-purple-500/20 transition-all duration-700 hover:scale-105 hover:-translate-y-2 group relative overflow-hidden ${index === 1 ? 'md:col-span-2 lg:col-span-1 md:scale-105' : ''}`}>
              {/* Floating decorative elements */}
              <div className="absolute top-4 right-4 opacity-50 group-hover:opacity-100 transition-opacity">
                <Award className="h-6 w-6 text-purple-400" />
              </div>
              
              {/* Gradient overlay on hover */}
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
              
              <div className="relative z-10">
                {/* Avatar */}
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                  {testimonial.avatar}
                </div>
                
                {/* Rating */}
                <div className="flex items-center gap-1 mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-6 w-6 fill-amber-400 text-amber-400 animate-pulse" style={{ animationDelay: `${i * 100}ms` }} />
                  ))}
                </div>
                
                <Quote className="h-10 w-10 text-purple-400 mb-6 group-hover:scale-110 transition-transform" />
                
                <p className="text-gray-200 leading-relaxed mb-8 text-lg font-light">
                  "{testimonial.content}"
                </p>
                
                <div className="border-t border-white/20 pt-6">
                  <p className="text-white font-bold text-xl">{testimonial.name}</p>
                  <p className="text-purple-200 font-medium">{testimonial.role}</p>
                  <p className="text-gray-400">{testimonial.company}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Enhanced YouTube Metrics */}
        <div className="text-center">
          <div className="backdrop-blur-3xl bg-gradient-to-r from-purple-500/15 to-pink-500/15 rounded-3xl border border-purple-500/30 p-12 shadow-2xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-600/10 to-pink-600/10"></div>
            <div className="relative z-10">
              <div className="flex justify-center items-center gap-4 mb-6">
                <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-pink-400 rounded-full animate-pulse delay-300"></div>
                <div className="w-4 h-4 bg-purple-300 rounded-full animate-pulse delay-700"></div>
              </div>
              <p className="text-4xl md:text-5xl font-bold text-white mb-4">Over 10K Views Per Episode</p>
              <p className="text-2xl text-purple-200 font-light">Join thousands of women transforming their careers</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

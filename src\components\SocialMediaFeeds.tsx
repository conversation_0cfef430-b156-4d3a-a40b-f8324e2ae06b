import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Play, ExternalLink } from "lucide-react";

interface SocialMediaPost {
  id: string;
  platform: 'youtube' | 'instagram' | 'tiktok';
  url: string;
  thumbnail?: string;
  title?: string;
}

const socialMediaPosts: SocialMediaPost[] = [
  // YouTube posts
  {
    id: 'yt1',
    platform: 'youtube',
    url: 'https://youtube.com/shorts/1A4pRKf9_QM?feature=share',
    title: 'Leadership Transformation Story'
  },
  {
    id: 'yt2',
    platform: 'youtube',
    url: 'https://youtube.com/shorts/1A4pRKf9_QM?feature=share',
    title: 'From Hood to Hooded Journey'
  },
  // Instagram posts
  {
    id: 'ig1',
    platform: 'instagram',
    url: 'https://www.instagram.com/alwaysbankonher/reel/DLD-GfzKIOf/',
    title: 'Executive Coaching Insights'
  },
  {
    id: 'ig2',
    platform: 'instagram',
    url: 'https://www.instagram.com/alwaysbankonher/reel/DK70JekMwTV/',
    title: 'Leadership Development'
  },
  {
    id: 'ig3',
    platform: 'instagram',
    url: 'https://www.instagram.com/alwaysbankonher/reel/DK70Ceqtcw7/',
    title: 'Empowerment Stories'
  },
  // TikTok posts
  {
    id: 'tt1',
    platform: 'tiktok',
    url: 'https://www.tiktok.com/@drjenniferrbishop/video/7515541921301826859',
    title: 'Breaking Barriers'
  },
  {
    id: 'tt2',
    platform: 'tiktok',
    url: 'https://www.tiktok.com/@drjenniferrbishop/video/7516283939523317038',
    title: 'Leadership Tips'
  },
  {
    id: 'tt3',
    platform: 'tiktok',
    url: 'https://www.tiktok.com/@drjenniferrbishop/video/7515171040759270702',
    title: 'Success Strategies'
  }
];

const platformConfig = {
  youtube: {
    name: 'YouTube',
    icon: '/images/youtube.png',
    color: 'from-red-500 to-red-600',
    bgColor: 'from-red-500/10 to-red-600/10'
  },
  instagram: {
    name: 'Instagram',
    icon: '/images/instagram.png',
    color: 'from-pink-500 to-purple-600',
    bgColor: 'from-pink-500/10 to-purple-600/10'
  },
  tiktok: {
    name: 'TikTok',
    icon: '/images/tiktok.png',
    color: 'from-black to-gray-800',
    bgColor: 'from-gray-800/10 to-black/10'
  }
};

export const SocialMediaFeeds = () => {
  const groupedPosts = {
    youtube: socialMediaPosts.filter(post => post.platform === 'youtube'),
    instagram: socialMediaPosts.filter(post => post.platform === 'instagram'),
    tiktok: socialMediaPosts.filter(post => post.platform === 'tiktok')
  };

  const handlePostClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto max-w-7xl">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-6">
            Follow My Journey
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Stay connected and inspired through my social media content where I share insights, 
            stories, and strategies for leadership transformation.
          </p>
        </div>

        {Object.entries(groupedPosts).map(([platform, posts]) => {
          const config = platformConfig[platform as keyof typeof platformConfig];
          
          return (
            <div key={platform} className="mb-16">
              {/* Platform Header */}
              <div className="flex items-center justify-center mb-8">
                <div className={`flex items-center gap-4 bg-gradient-to-r ${config.bgColor} backdrop-blur-sm rounded-2xl px-8 py-4 border border-white/10`}>
                  <div className="relative w-12 h-12">
                    <div className={`absolute inset-0 bg-gradient-to-r ${config.color} p-0.5 rounded-xl`}>
                      <div className="w-full h-full bg-slate-900 rounded-xl flex items-center justify-center">
                        <img 
                          src={config.icon} 
                          alt={config.name}
                          className="w-8 h-8 object-contain"
                        />
                      </div>
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold text-white">{config.name}</h3>
                </div>
              </div>

              {/* Posts Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {posts.map((post) => (
                  <Card 
                    key={post.id} 
                    className="group cursor-pointer transform hover:scale-105 transition-all duration-300 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm border border-white/20 hover:border-white/40"
                    onClick={() => handlePostClick(post.url)}
                  >
                    <CardContent className="p-0">
                      <div className="relative aspect-video bg-gradient-to-br from-slate-800 to-slate-900 rounded-t-lg overflow-hidden">
                        {/* Thumbnail placeholder with gradient border */}
                        <div className={`absolute inset-0 bg-gradient-to-r ${config.color} p-1`}>
                          <div className="w-full h-full bg-gradient-to-br from-slate-800 to-slate-900 rounded-t-lg flex items-center justify-center">
                            <div className="text-center">
                              <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${config.color} rounded-full flex items-center justify-center`}>
                                <img 
                                  src={config.icon} 
                                  alt={config.name}
                                  className="w-8 h-8 object-contain"
                                />
                              </div>
                              <p className="text-white/70 text-sm">Video Content</p>
                            </div>
                          </div>
                        </div>
                        
                        {/* Play button overlay */}
                        <div className="absolute inset-0 flex items-center justify-center bg-black/20 group-hover:bg-black/40 transition-all duration-300">
                          <div className={`w-16 h-16 bg-gradient-to-r ${config.color} rounded-full flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300`}>
                            <Play className="w-6 h-6 text-white ml-1" fill="currentColor" />
                          </div>
                        </div>
                      </div>
                      
                      <div className="p-6">
                        <h4 className="text-white font-semibold mb-3 group-hover:text-purple-300 transition-colors">
                          {post.title}
                        </h4>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full border-white/20 text-white hover:bg-white/10"
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePostClick(post.url);
                          }}
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Watch on {config.name}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </section>
  );
};

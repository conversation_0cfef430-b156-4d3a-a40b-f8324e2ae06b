
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Crown } from "lucide-react";

export const CallToAction = () => {
  return (
    <section className="py-32 px-4 relative overflow-hidden">
      {/* Enhanced background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-amber-600/30 to-orange-600/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-emerald-500/20 to-teal-600/20 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>
      
      <div className="max-w-5xl mx-auto text-center relative z-10">
        <div className="backdrop-blur-3xl bg-gradient-to-br from-amber-500/15 to-orange-600/15 rounded-[2rem] border border-amber-500/30 p-16 shadow-2xl relative overflow-hidden">
          {/* Animated floating elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-8 left-8 w-4 h-4 bg-amber-400 rounded-full animate-bounce"></div>
            <div className="absolute top-12 right-12 w-3 h-3 bg-orange-400 rounded-full animate-bounce delay-300"></div>
            <div className="absolute bottom-12 left-16 w-5 h-5 bg-amber-300 rounded-full animate-bounce delay-700"></div>
            <div className="absolute bottom-8 right-8">
              <Crown className="h-6 w-6 text-amber-400 animate-pulse" />
            </div>
            <div className="absolute top-1/2 left-4">
              <Star className="h-4 w-4 text-orange-400 animate-spin" style={{ animationDuration: '8s' }} />
            </div>
          </div>
          
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-amber-900/10 via-transparent to-orange-900/10 rounded-[2rem]"></div>
          
          <div className="relative z-10">
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-amber-500/30 to-orange-500/30 backdrop-blur-sm rounded-full px-8 py-3 mb-8 border border-amber-500/40 shadow-lg">
              <Sparkles className="h-5 w-5 text-amber-300 animate-pulse" />
              <span className="text-amber-100 font-semibold">Limited Time Opportunity</span>
              <Sparkles className="h-5 w-5 text-amber-300 animate-pulse delay-500" />
            </div>
            
            <h2 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-white via-amber-200 to-orange-200 bg-clip-text text-transparent mb-8 leading-tight">
              Your C‑Suite Breakthrough
              <span className="block text-4xl md:text-6xl mt-2">Starts Here</span>
            </h2>
            
            <p className="text-2xl text-gray-200 mb-12 max-w-3xl mx-auto leading-relaxed font-light">
              Book your free call—let's define the next chapter in your executive journey.
            </p>
            
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-amber-500 via-orange-500 to-amber-600 hover:from-amber-600 hover:via-orange-600 hover:to-amber-700 text-white font-bold px-16 py-8 rounded-full text-2xl shadow-2xl hover:shadow-amber-500/30 transition-all duration-500 hover:scale-110 group relative overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <span className="relative z-10">Reserve My Free Call</span>
              <ArrowRight className="ml-4 h-7 w-7 group-hover:translate-x-2 transition-transform duration-300 relative z-10" />
            </Button>
            
            <p className="text-amber-200/80 mt-6 text-lg">
              ✨ Transform your career trajectory in just 15 minutes
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};


import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, Instagram, Youtube, Crown, <PERSON>rk<PERSON>, Heart } from "lucide-react";

export const Footer = () => {
  return (
    <footer className="py-32 px-4 border-t border-white/20 relative overflow-hidden">
      {/* Enhanced background */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-t from-slate-900 via-purple-900/50 to-transparent"></div>
        <div className="absolute top-1/2 left-1/4 w-64 h-64 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-72 h-72 bg-gradient-to-r from-amber-600/20 to-orange-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
      
      <div className="max-w-7xl mx-auto relative z-10">
        {/* Enhanced Final CTA */}
        <div className="text-center mb-20">
          <div className="backdrop-blur-3xl bg-gradient-to-br from-white/10 to-purple-500/10 rounded-[2rem] border border-white/20 p-12 md:p-16 shadow-2xl relative overflow-hidden">
            {/* Floating decorative elements */}
            <div className="absolute inset-0 overflow-hidden">
              <div className="absolute top-8 left-8">
                <Crown className="h-10 w-10 text-amber-400 animate-bounce" />
              </div>
              <div className="absolute top-12 right-12">
                <Sparkles className="h-8 w-8 text-purple-400 animate-pulse" />
              </div>
              <div className="absolute bottom-8 right-8">
                <Heart className="h-6 w-6 text-pink-400 animate-pulse" />
              </div>
              <div className="absolute bottom-12 left-12">
                <div className="w-4 h-4 bg-amber-400 rounded-full animate-bounce delay-700"></div>
              </div>
            </div>
            
            {/* Gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-amber-500/5 rounded-[2rem]"></div>
            
            <div className="relative z-10">
              <h2 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-white via-purple-200 to-amber-200 bg-clip-text text-transparent mb-8 leading-tight">
                Feeling seen. Wanting more.
                <span className="block text-5xl md:text-7xl mt-4">Ready to rise.</span>
              </h2>
              
              <p className="text-xl text-gray-300 mb-10 max-w-2xl mx-auto leading-relaxed">
                Your legacy awaits. Take the first step toward executive excellence.
              </p>
              
              <Button 
                size="lg" 
                className="bg-gradient-to-r from-amber-500 via-orange-500 to-amber-600 hover:from-amber-600 hover:via-orange-600 hover:to-amber-700 text-white font-bold px-16 py-8 rounded-full text-2xl shadow-2xl hover:shadow-amber-500/30 transition-all duration-500 hover:scale-110 group relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <span className="relative z-10">Book Your Free Strategy Session</span>
                <ArrowRight className="ml-4 h-7 w-7 group-hover:translate-x-2 transition-transform duration-300 relative z-10" />
              </Button>
            </div>
          </div>
        </div>
        
        {/* Enhanced Social Links */}
        <div className="flex justify-center items-center gap-8 mb-16">
          {[
            { icon: Youtube, color: "from-red-500 to-red-600", hoverColor: "hover:text-red-300" },
            { icon: Instagram, color: "from-pink-500 to-purple-600", hoverColor: "hover:text-pink-300" }
          ].map((social, index) => (
            <a key={index} href="#" className={`p-6 rounded-full bg-gradient-to-r ${social.color} border border-white/30 hover:scale-125 transition-all duration-300 group shadow-xl hover:shadow-2xl backdrop-blur-sm`}>
              <social.icon className={`h-8 w-8 text-white ${social.hoverColor} transition-colors duration-300`} />
            </a>
          ))}
        </div>
        
        {/* Enhanced Footer Text */}
        <div className="text-center">
          <div className="backdrop-blur-sm bg-white/5 rounded-2xl p-8 border border-white/10">
            <p className="text-white text-lg mb-4 font-medium">© 2024 AlwaysBankOnHer. All rights reserved.</p>
            <p className="text-purple-200 font-light text-lg italic">
              "From Hood to Hooded - Building Legacy, Breaking Barriers"
            </p>
            <div className="flex justify-center items-center gap-2 mt-4">
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse delay-300"></div>
              <div className="w-2 h-2 bg-pink-400 rounded-full animate-pulse delay-700"></div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

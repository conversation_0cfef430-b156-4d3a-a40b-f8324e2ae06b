
import { Check, Heart, Target, Lightbulb } from "lucide-react";

export const AboutSection = () => {
  const keyStatements = [
    {
      icon: Heart,
      text: "No judgment. Just exploration.",
      color: "from-pink-500 to-rose-500"
    },
    {
      icon: Target,
      text: "Future‑focused, insight‑driven coaching.",
      color: "from-purple-500 to-indigo-500"
    },
    {
      icon: Lightbulb,
      text: "Your aspirations deserve strategy.",
      color: "from-amber-500 to-orange-500"
    }
  ];

  return (
    <section className="py-20 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent mb-6">
            About You – The Coach
          </h2>
        </div>
        
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Text content */}
          <div className="backdrop-blur-xl bg-white/5 rounded-3xl border border-white/10 p-8 shadow-xl">
            <p className="text-gray-300 text-lg leading-relaxed mb-8">
              As a professional career coach, I guide creative, comprehensive, and critical thinking in Black women ready to rise. Through open‑ended questions and curiosity, I help you unearth your values, purpose, and vision—aligning career and life goals for lasting fulfillment.
            </p>
            
            <div className="space-y-6">
              {keyStatements.map((statement, index) => (
                <div key={index} className="flex items-center gap-4 group hover:scale-105 transition-transform duration-300">
                  <div className={`p-3 rounded-full bg-gradient-to-r ${statement.color} shadow-lg`}>
                    <statement.icon className="h-5 w-5 text-white" />
                  </div>
                  <p className="text-white font-medium text-lg">{statement.text}</p>
                </div>
              ))}
            </div>
          </div>
          
          {/* Image placeholder with glass effect */}
          <div className="relative">
            <div className="backdrop-blur-xl bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-3xl border border-white/20 aspect-square flex items-center justify-center shadow-2xl">
              <div className="text-center text-white">
                <div className="w-32 h-32 bg-gradient-to-br from-amber-500 to-orange-600 rounded-full mx-auto mb-4 flex items-center justify-center shadow-xl">
                  <span className="text-4xl font-bold">👑</span>
                </div>
                <p className="text-lg font-medium">Your Photo Here</p>
                <p className="text-sm text-gray-300">Adding relatability and trust</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};


import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Mail, Users, Crown, Sparkles } from "lucide-react";

export const NewsletterSection = () => {
  return (
    <section className="py-32 px-4 relative overflow-hidden">
      {/* Enhanced background */}
      <div className="absolute inset-0">
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-gradient-to-r from-emerald-600/20 to-teal-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-gradient-to-r from-teal-600/20 to-cyan-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
      
      <div className="max-w-5xl mx-auto relative z-10">
        <div className="backdrop-blur-3xl bg-gradient-to-br from-emerald-500/15 to-teal-600/15 rounded-[2rem] border border-emerald-500/30 p-12 md:p-16 shadow-2xl text-center relative overflow-hidden">
          {/* Floating decorative elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-8 left-8">
              <Crown className="h-8 w-8 text-emerald-400 animate-bounce" />
            </div>
            <div className="absolute top-12 right-12">
              <Sparkles className="h-6 w-6 text-teal-400 animate-pulse" />
            </div>
            <div className="absolute bottom-8 right-8">
              <div className="w-5 h-5 bg-emerald-400 rounded-full animate-pulse"></div>
            </div>
            <div className="absolute bottom-12 left-12">
              <div className="w-3 h-3 bg-teal-400 rounded-full animate-bounce delay-500"></div>
            </div>
          </div>
          
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-emerald-900/10 via-transparent to-teal-900/10 rounded-[2rem]"></div>
          
          <div className="relative z-10">
            <div className="inline-flex items-center gap-3 bg-emerald-500/30 backdrop-blur-sm rounded-full px-8 py-3 mb-8 border border-emerald-500/40 shadow-lg">
              <Users className="h-5 w-5 text-emerald-300" />
              <span className="text-emerald-200 font-semibold">Join the Community</span>
              <Crown className="h-5 w-5 text-emerald-300" />
            </div>
            
            <h2 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-white via-emerald-200 to-teal-200 bg-clip-text text-transparent mb-8 leading-tight">
              Join the Legacy
              <span className="block text-4xl md:text-6xl mt-2">Network</span>
            </h2>
            
            <p className="text-2xl text-gray-200 mb-12 max-w-3xl mx-auto leading-relaxed font-light">
              Subscribe for exclusive leadership insights, episode drops, and executive-level coaching strategies delivered to your inbox.
            </p>
            
            {/* Enhanced form */}
            <div className="backdrop-blur-sm bg-white/10 rounded-2xl p-8 mb-8 border border-white/20">
              <div className="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto mb-6">
                <Input 
                  type="text" 
                  placeholder="Your name" 
                  className="bg-white/20 border-white/30 text-white placeholder:text-gray-300 rounded-full h-14 px-6 text-lg backdrop-blur-sm focus:bg-white/25 transition-all duration-300"
                />
                <Input 
                  type="email" 
                  placeholder="Your email" 
                  className="bg-white/20 border-white/30 text-white placeholder:text-gray-300 rounded-full h-14 px-6 text-lg backdrop-blur-sm focus:bg-white/25 transition-all duration-300"
                />
              </div>
              
              <Button 
                size="lg" 
                className="bg-gradient-to-r from-emerald-500 via-teal-500 to-emerald-600 hover:from-emerald-600 hover:via-teal-600 hover:to-emerald-700 text-white font-bold px-12 py-6 rounded-full shadow-2xl hover:shadow-emerald-500/30 transition-all duration-500 hover:scale-105 group text-xl relative overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <Mail className="mr-3 h-6 w-6 relative z-10" />
                <span className="relative z-10">Join the Legacy Network</span>
              </Button>
            </div>
            
            {/* Benefits */}
            <div className="grid md:grid-cols-3 gap-6 text-center">
              {[
                { icon: "📈", text: "Weekly Leadership Insights" },
                { icon: "🎬", text: "Early Episode Access" },
                { icon: "👑", text: "Exclusive Coaching Tips" }
              ].map((benefit, index) => (
                <div key={index} className="backdrop-blur-sm bg-white/5 rounded-xl p-4 border border-white/10 hover:scale-105 transition-transform duration-300">
                  <div className="text-2xl mb-2">{benefit.icon}</div>
                  <p className="text-emerald-200 font-medium">{benefit.text}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};


import { HeroSection } from "@/components/HeroSection";
import { CoachSection } from "@/components/CoachSection";
import { QuotesCarousel } from "@/components/QuotesCarousel";
import { HoodToHoodedSeries } from "@/components/HoodToHoodedSeries";
import { SocialMediaFeeds } from "@/components/SocialMediaFeeds";
import { BannerSection } from "@/components/BannerSection";
import { ThemeSwitcher } from "@/components/ThemeSwitcher";
import { AboutSection } from "@/components/AboutSection";
import { ServicesSection } from "@/components/ServicesSection";
import { CallToAction } from "@/components/CallToAction";
import { TestimonialsSection } from "@/components/TestimonialsSection";
import { NewsletterSection } from "@/components/NewsletterSection";
import { Footer } from "@/components/Footer";

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <ThemeSwitcher />
      <HeroSection />
      <CoachSection />
      <QuotesCarousel />
      <HoodToHoodedSeries />
      <BannerSection />
      <SocialMediaFeeds />
      <AboutSection />
      <ServicesSection />
      <CallToAction />
      <TestimonialsSection />
      <NewsletterSection />
      <Footer />
    </div>
  );
};

export default Index;

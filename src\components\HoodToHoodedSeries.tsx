import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useTheme } from "@/hooks/useTheme";
import { ArrowRight, BookOpen, Video, Podcast, Users } from "lucide-react";

export const HoodToHoodedSeries = () => {
  const { theme } = useTheme();

  const seriesFeatures = [
    {
      icon: BookOpen,
      title: "Masterclass Series",
      description: "Comprehensive leadership development modules",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: Video,
      title: "Video Content",
      description: "Real stories, real transformations",
      color: "from-pink-500 to-amber-500"
    },
    {
      icon: Podcast,
      title: "Podcast Episodes",
      description: "Weekly insights and interviews",
      color: "from-amber-500 to-orange-500"
    },
    {
      icon: Users,
      title: "Community Access",
      description: "Connect with like-minded leaders",
      color: "from-orange-500 to-red-500"
    }
  ];

  const benefits = [
    "Break through systemic barriers",
    "Develop authentic leadership style",
    "Build executive presence",
    "Navigate corporate politics",
    "Create lasting impact",
    "Build generational wealth"
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto max-w-7xl">
        <div className="text-center mb-16">
          <Badge className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-purple-200 border-purple-500/30 mb-6">
            Signature Program
          </Badge>
          
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-6">
            From Hood to Hooded Series
          </h2>
          
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12">
            A comprehensive transformation program designed specifically for Black women 
            ready to rise from their circumstances to executive excellence.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Logo and Program Overview */}
          <div className="text-center lg:text-left">
            <div className="relative w-64 h-64 mx-auto lg:mx-0 mb-8">
              {/* Gradient border */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500 via-pink-500 to-amber-500 p-1 rounded-3xl">
                <div className="w-full h-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 rounded-3xl flex items-center justify-center">
                  <img 
                    src={theme === 'dark' ? '/images/tagtp.png' : '/images/tag.png'}
                    alt="From Hood to Hooded Logo" 
                    className="w-48 h-48 object-contain transition-all duration-500"
                  />
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <h3 className="text-2xl md:text-3xl font-bold text-white">
                Transform Your Leadership Journey
              </h3>
              
              <p className="text-gray-300 leading-relaxed">
                This isn't just another leadership program. It's a movement that honors your story, 
                acknowledges your struggles, and provides the tools, strategies, and community you need 
                to reach the C-suite with authenticity and power.
              </p>

              <div className="space-y-3">
                <h4 className="text-lg font-semibold text-purple-300">What You'll Gain:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"></div>
                      <span className="text-gray-300 text-sm">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Program Features */}
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-8 text-center lg:text-left">
              Program Components
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {seriesFeatures.map((feature, index) => (
                <Card key={index} className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm border border-white/20 hover:border-white/40 transition-all duration-300 group">
                  <CardContent className="p-6">
                    <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="text-lg font-semibold text-white mb-2">{feature.title}</h4>
                    <p className="text-gray-400 text-sm">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Testimonial Quote */}
            <Card className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-purple-500/30 mt-8">
              <CardContent className="p-6">
                <blockquote className="text-white italic text-lg mb-4">
                  "This program didn't just change my career—it changed my life. I went from feeling 
                  invisible in meetings to leading them with confidence."
                </blockquote>
                <cite className="text-purple-300 font-medium">— Sarah M., VP of Operations</cite>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <Card className="bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-amber-500/20 backdrop-blur-sm border border-white/20 p-8 md:p-12">
            <CardContent className="p-0">
              <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Begin Your Transformation?
              </h3>
              
              <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Join hundreds of Black women who have transformed their careers and lives 
                through the From Hood to Hooded Series.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-purple-600 via-pink-600 to-amber-600 hover:from-purple-700 hover:via-pink-700 hover:to-amber-700 text-white font-semibold px-8 py-4 rounded-full shadow-2xl transform hover:scale-105 transition-all duration-300 group"
                >
                  Enroll Now - Early Bird Pricing
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>
                
                <Button 
                  variant="outline" 
                  size="lg" 
                  className="border-2 border-white/30 text-white hover:bg-white/10 px-8 py-4 rounded-full backdrop-blur-sm"
                >
                  Learn More
                </Button>
              </div>
              
              <p className="text-sm text-gray-400 mt-6">
                Limited spots available • Next cohort starts soon
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Quote } from "lucide-react";

interface CarouselItem {
  id: string;
  type: 'quote' | 'image';
  content: string;
  author?: string;
  image?: string;
  alt?: string;
}

const carouselItems: CarouselItem[] = [
  {
    id: 'img1',
    type: 'image',
    content: '',
    image: '/images/eva.jpg',
    alt: 'Eva - Leadership Journey'
  },
  {
    id: 'quote1',
    type: 'quote',
    content: "Black women are the only flowers expected to bloom while not being watered.",
    author: "Anonymous"
  },
  {
    id: 'img2',
    type: 'image',
    content: '',
    image: '/images/eva2.jpg',
    alt: 'Eva - Transformation Story'
  },
  {
    id: 'quote2',
    type: 'quote',
    content: "If you accept the call to leadership, you must be willing to be misunderstood, criticized, opposed, accused, and even rejected.",
    author: "Leadership Wisdom"
  },
  {
    id: 'img3',
    type: 'image',
    content: '',
    image: '/images/c1.jpg',
    alt: 'Client Success Story 1'
  },
  {
    id: 'quote3',
    type: 'quote',
    content: "MAYBE IF YOU WERE NICER TO ME I WOULDN'T HAVE TO ACT ON THE RACIAL STEREOTYPES I'VE INTERNALIZED ABOUT YOU.",
    author: "Truth Speaker"
  },
  {
    id: 'img4',
    type: 'image',
    content: '',
    image: '/images/c2.jpg',
    alt: 'Client Success Story 2'
  },
  {
    id: 'quote4',
    type: 'quote',
    content: "If you are free, you need to free somebody else. If you have some power, then your job is to empower somebody else.",
    author: "Toni Morrison"
  },
  {
    id: 'img5',
    type: 'image',
    content: '',
    image: '/images/c3.jpg',
    alt: 'Client Success Story 3'
  },
  {
    id: 'quote5',
    type: 'quote',
    content: "Diversity is a fact. Equity is a choice. Inclusion is an action. Belonging is an outcome.",
    author: "Equity Leader"
  },
  {
    id: 'img6',
    type: 'image',
    content: '',
    image: '/images/c4.jpg',
    alt: 'Client Success Story 4'
  },
  {
    id: 'quote6',
    type: 'quote',
    content: "Sit at the table of the dreamers, the go-getters, the star gazers, the warriors, the ones who fight for others, the conversation hits different. It's where you'll truly learn how to live.",
    author: "Visionary"
  },
  {
    id: 'img7',
    type: 'image',
    content: '',
    image: '/images/c5.jpg',
    alt: 'Client Success Story 5'
  },
  {
    id: 'quote7',
    type: 'quote',
    content: "You have to leave behind the 'comfortable' to experience the spectacular!",
    author: "Growth Mindset"
  }
];

export const QuotesCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === carouselItems.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const goToPrevious = () => {
    setIsAutoPlaying(false);
    setCurrentIndex(currentIndex === 0 ? carouselItems.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setIsAutoPlaying(false);
    setCurrentIndex(currentIndex === carouselItems.length - 1 ? 0 : currentIndex + 1);
  };

  const goToSlide = (index: number) => {
    setIsAutoPlaying(false);
    setCurrentIndex(index);
  };

  const currentItem = carouselItems[currentIndex];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-6">
            Inspiration & Stories
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Powerful quotes and transformational stories that fuel the journey from hood to hooded.
          </p>
        </div>

        <div className="relative">
          {/* Main Carousel Card */}
          <Card className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 shadow-2xl min-h-[500px]">
            <CardContent className="p-0">
              {currentItem.type === 'quote' ? (
                <div className="flex items-center justify-center min-h-[500px] p-8 md:p-16">
                  <div className="text-center max-w-4xl">
                    <Quote className="w-16 h-16 mx-auto mb-8 text-purple-400 opacity-50" />
                    <blockquote className="text-2xl md:text-3xl lg:text-4xl font-light text-white leading-relaxed mb-8">
                      "{currentItem.content}"
                    </blockquote>
                    {currentItem.author && (
                      <cite className="text-lg text-purple-300 font-medium">
                        — {currentItem.author}
                      </cite>
                    )}
                  </div>
                </div>
              ) : (
                <div className="relative min-h-[500px]">
                  {/* Gradient border for image */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500 via-pink-500 to-amber-500 p-1 rounded-lg">
                    <div className="w-full h-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 rounded-lg overflow-hidden">
                      <img 
                        src={currentItem.image} 
                        alt={currentItem.alt}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-slate-900/50 via-transparent to-transparent" />
                    </div>
                  </div>
                  
                  {/* Image overlay text */}
                  <div className="absolute bottom-8 left-8 right-8">
                    <h3 className="text-2xl md:text-3xl font-bold text-white mb-2">
                      {currentItem.alt}
                    </h3>
                    <p className="text-gray-300">
                      Transformation in action
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20"
            onClick={goToPrevious}
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20"
            onClick={goToNext}
          >
            <ChevronRight className="h-6 w-6" />
          </Button>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center mt-8 space-x-2">
          {carouselItems.map((_, index) => (
            <button
              key={index}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-gradient-to-r from-purple-400 to-pink-400 scale-125'
                  : 'bg-white/30 hover:bg-white/50'
              }`}
              onClick={() => goToSlide(index)}
            />
          ))}
        </div>

        {/* Auto-play indicator */}
        <div className="text-center mt-6">
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-white"
            onClick={() => setIsAutoPlaying(!isAutoPlaying)}
          >
            {isAutoPlaying ? 'Pause' : 'Play'} Auto-scroll
          </Button>
        </div>
      </div>
    </section>
  );
};
